class AgentPortal::ChannelsController < AgentPortal::ApplicationController
  before_action :set_channel, only: [ :show, :edit, :update, :destroy ]

  def index
    current = (params[:page].presence || 1).to_i.clamp(1, 1000000)
    channel_scope = Current.agent.channels.includes(:promotion_type)

    # 添加搜索功能
    search_params = params[:search]
    if search_params.present?
      channel_scope = channel_scope.where("name LIKE ? OR nickname LIKE ?", "%#{search_params}%", "%#{search_params}%")
    end

    # 按状态筛选
    if params[:status].present?
      channel_scope = channel_scope.where(status: params[:status])
    end

    # 按推广类型筛选
    if params[:promotion_type_id].present?
      channel_scope = channel_scope.where(promotion_type_id: params[:promotion_type_id])
    end

    channel_scope = channel_scope.order(id: :desc)
    @pagy, @channels = pagy(channel_scope, limit: 50, page: current)

    # 为搜索表单准备数据
    @promotion_types = PromotionType.enabled
  end

  def show
  end

  def new
    @channel = Current.agent.channels.build
    @promotion_types = PromotionType.enabled
  end

  def create
    @channel = Current.agent.channels.build(channel_params)
    @channel.nickname ||= @channel.name
    @channel.password = Channel::DEFAULT_PASSWORD if @channel.password.blank?
    @channel.timezone ||= "UTC"

    if @channel.save
      redirect_to agent_portal_channel_path(@channel), notice: "渠道 #{@channel.name} 创建成功"
    else
      @promotion_types = PromotionType.enabled
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @promotion_types = PromotionType.enabled
  end

  def update
    if @channel.update(channel_params)
      redirect_to agent_portal_channel_path(@channel), notice: "渠道 #{@channel.name} 更新成功"
    else
      @promotion_types = PromotionType.enabled
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @channel.destroy
    redirect_to agent_portal_channels_path, notice: "渠道 #{@channel.name} 已删除"
  end

  private

  def set_channel
    @channel = Current.agent.channels.find(params[:id])
  end

  def channel_params
    params.require(:channel).permit(:name, :nickname, :remark, :status,
      :promotion_type_id, :advertiser, :pixel_id, :pixel_token)
  end
end
