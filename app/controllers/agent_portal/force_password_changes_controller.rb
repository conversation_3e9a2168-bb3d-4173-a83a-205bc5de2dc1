module AgentPortal
  class ForcePasswordChangesController < AgentPortal::ApplicationController
    skip_before_action :check_default_password
    layout "blank_agent"

    def show
      @agent = Current.agent
      # 如果不是使用默认密码，重定向到首页
      unless @agent.using_default_password?
        redirect_to agent_portal_root_path
        return
      end
    end

    def update
      @agent = Current.agent
      
      # 验证当前密码是否为默认密码
      unless @agent.using_default_password?
        redirect_to agent_portal_root_path, alert: "无效的操作"
        return
      end

      # 验证新密码
      if agent_params[:password].blank?
        @agent.errors.add(:password, "不能为空")
        render :show, status: :unprocessable_entity
        return
      end

      if agent_params[:password] != agent_params[:password_confirmation]
        @agent.errors.add(:password_confirmation, "与新密码不匹配")
        render :show, status: :unprocessable_entity
        return
      end

      # 不能设置为默认密码
      if agent_params[:password] == Agent::DEFAULT_PASSWORD
        @agent.errors.add(:password, "不能设置为默认密码")
        render :show, status: :unprocessable_entity
        return
      end

      # 更新密码
      if @agent.update(password: agent_params[:password])
        redirect_to agent_portal_root_path, notice: "密码修改成功，现在可以正常使用系统了"
      else
        render :show, status: :unprocessable_entity
      end
    end

    private

    def agent_params
      params.require(:agent).permit(:password, :password_confirmation)
    end
  end
end
