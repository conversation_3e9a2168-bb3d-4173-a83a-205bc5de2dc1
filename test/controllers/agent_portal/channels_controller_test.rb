require "test_helper"

class AgentPortal::ChannelsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @agent = agents(:active_agent)
    @promotion_type = promotion_types(:facebook)
    @channel = channels(:active_channel)
    # 确保测试渠道属于测试代理
    @channel.update!(agent: @agent)
  end

  test "should get index" do
    sign_in_as @agent
    get agent_portal_channels_path
    assert_response :success
    assert_select "h1", "我的渠道"
  end

  test "should show channel" do
    sign_in_as @agent
    get agent_portal_channel_path(@channel)
    assert_response :success
    assert_select "h1", text: /渠道详情/
  end

  test "should get new" do
    sign_in_as @agent
    get new_agent_portal_channel_path
    assert_response :success
    assert_select "h1", text: /添加渠道/
  end

  test "should create channel" do
    sign_in_as @agent
    assert_difference("Channel.count") do
      post agent_portal_channels_path, params: {
        channel: {
          name: "TestChannel",
          nickname: "Test",
          email_address: "<EMAIL>",
          mobile: "13800138000",
          promotion_type_id: @promotion_type.id,
          timezone: "UTC",
          status: "actived"
        }
      }
    end



    assert_redirected_to agent_portal_channel_path(Channel.last)
    assert_equal "渠道 TestChannel 创建成功", flash[:notice]

    # 验证渠道属于当前代理
    assert_equal @agent, Channel.last.agent
  end

  test "should get edit" do
    sign_in_as @agent
    get edit_agent_portal_channel_path(@channel)
    assert_response :success
    assert_select "h1", text: /编辑渠道/
  end

  test "should update channel" do
    sign_in_as @agent
    patch agent_portal_channel_path(@channel), params: {
      channel: {
        name: "UpdatedChannel",
        nickname: "Updated"
      }
    }
    assert_redirected_to agent_portal_channel_path(@channel)
    assert_equal "渠道 UpdatedChannel 更新成功", flash[:notice]
  end

  test "should destroy channel" do
    sign_in_as @agent
    assert_difference("Channel.count", -1) do
      delete agent_portal_channel_path(@channel)
    end

    assert_redirected_to agent_portal_channels_path
  end

  test "should only show agent's own channels" do
    # 创建另一个代理和渠道
    other_agent = Agent.create!(
      name: "OtherAgent",
      password: "password123",
      timezone: "UTC"
    )
    other_channel = Channel.create!(
      name: "OtherChannel",
      password: "password123",
      agent: other_agent,
      promotion_type: @promotion_type,
      timezone: "UTC"
    )

    sign_in_as @agent
    get agent_portal_channels_path
    assert_response :success

    # 应该只能看到自己的渠道
    assert_select "a[href='#{agent_portal_channel_path(@channel)}']"
    assert_select "a[href='#{agent_portal_channel_path(other_channel)}']", count: 0
  end

  test "should not access other agent's channel" do
    # 使用 fixture 中的其他代理的渠道
    other_channel = channels(:google_channel)  # 这个渠道属于 inactive_agent

    sign_in_as @agent  # 登录为 active_agent

    # 尝试访问其他代理的渠道应该返回 404
    get agent_portal_channel_path(other_channel)
    assert_response :not_found
  end

  test "should require authentication" do
    get agent_portal_channels_path
    assert_response :redirect
    assert_redirected_to new_agent_portal_session_path
  end

  test "should search channels by name" do
    sign_in_as @agent
    get agent_portal_channels_path, params: { search: @channel.name }
    assert_response :success
    assert_select "a[href='#{agent_portal_channel_path(@channel)}']"
  end

  test "should search channels by nickname" do
    sign_in_as @agent
    get agent_portal_channels_path, params: { search: @channel.nickname }
    assert_response :success
    assert_select "a[href='#{agent_portal_channel_path(@channel)}']"
  end

  test "should filter channels by status" do
    sign_in_as @agent
    get agent_portal_channels_path, params: { status: "actived" }
    assert_response :success
    assert_select "a[href='#{agent_portal_channel_path(@channel)}']"
  end

  test "should filter channels by promotion type" do
    sign_in_as @agent
    get agent_portal_channels_path, params: { promotion_type_id: @promotion_type.id }
    assert_response :success
    assert_select "a[href='#{agent_portal_channel_path(@channel)}']"
  end

  test "should return empty result for non-matching search" do
    sign_in_as @agent
    get agent_portal_channels_path, params: { search: "nonexistent" }
    assert_response :success
    assert_select "a[href='#{agent_portal_channel_path(@channel)}']", count: 0
  end
end
