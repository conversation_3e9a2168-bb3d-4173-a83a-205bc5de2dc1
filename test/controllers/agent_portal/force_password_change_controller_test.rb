require "test_helper"

class AgentPortal::ForcePasswordChangesControllerTest < ActionDispatch::IntegrationTest
  def setup
    @agent = Agent.create!(
      name: "test_agent",
      nickname: "Test Agent",
      password: Agent::DEFAULT_PASSWORD,
      timezone: "UTC",
      totp_secret: ROTP::Base32.random_base32
    )
  end

  test "should show force password change page when using default password" do
    # 模拟登录
    post agent_portal_session_path, params: {
      email_address: @agent.name,
      password: Agent::DEFAULT_PASSWORD
    }
    
    # 应该重定向到强制修改密码页面
    assert_redirected_to agent_portal_force_password_change_path
    
    # 访问强制修改密码页面
    get agent_portal_force_password_change_path
    assert_response :success
    assert_select "h2", "安全提醒"
  end

  test "should redirect to home if not using default password" do
    # 修改密码为非默认密码
    @agent.update!(password: "new_password123")
    
    # 模拟登录
    post agent_portal_session_path, params: {
      email_address: @agent.name,
      password: "new_password123"
    }
    
    # 应该重定向到首页
    assert_redirected_to agent_portal_root_path
    
    # 尝试访问强制修改密码页面应该重定向到首页
    get agent_portal_force_password_change_path
    assert_redirected_to agent_portal_root_path
  end

  test "should update password successfully" do
    # 模拟登录使用默认密码
    post agent_portal_session_path, params: {
      email_address: @agent.name,
      password: Agent::DEFAULT_PASSWORD
    }
    
    # 修改密码
    patch agent_portal_force_password_change_path, params: {
      agent: {
        password: "new_secure_password123",
        password_confirmation: "new_secure_password123"
      }
    }
    
    assert_redirected_to agent_portal_root_path
    assert_equal "密码修改成功，现在可以正常使用系统了", flash[:notice]
    
    # 验证密码已更改
    @agent.reload
    assert @agent.authenticate("new_secure_password123")
    assert_not @agent.using_default_password?
  end

  test "should not allow setting default password as new password" do
    # 模拟登录使用默认密码
    post agent_portal_session_path, params: {
      email_address: @agent.name,
      password: Agent::DEFAULT_PASSWORD
    }
    
    # 尝试设置默认密码作为新密码
    patch agent_portal_force_password_change_path, params: {
      agent: {
        password: Agent::DEFAULT_PASSWORD,
        password_confirmation: Agent::DEFAULT_PASSWORD
      }
    }
    
    assert_response :unprocessable_entity
    assert_select ".text-red-500", /不能设置为默认密码/
  end
end
